{"cells": [{"cell_type": "markdown", "id": "817705cd", "metadata": {"id": "817705cd"}, "source": ["# Assignment 2 – Question&nbsp;1 Notebook  \n", "**Tile‑Coded Approximate Q‑Learning for Drone Navigation**\n", "\n", "This notebook maps **each sub‑question or sub-part** of Question&nbsp;1 to specific cells:\n", "\n", "| Sub‑question | Notebook section |\n", "|--------------|------------------|\n", "| (a) Feature representation & update equation | See markdown Section A and the Tile Coder + `update` method |\n", "| (b) Implementation (environment + agent) | Section B code cells |\n", "| (c) Hyper‑parameter tuning | Section C code cell |\n", "| (d) Performance evaluation | Section D code cell |\n"]}, {"cell_type": "markdown", "id": "22455cbb", "metadata": {"id": "22455cbb"}, "source": ["## **A. Sub‑question (a): Feature Representation & Update Equation**\n", "\n", "We discretise the continuous position $(x,y)$ using a uniform grid  \n", "implemented by **`TileCoder`**.  \n", "The linear value function for a given action \\(a\\) is  \n", "\n", "$\n", "\\hat Q(s,a) = \\mathbf w_a^\\top \\; \\boldsymbol\\phi(s)\n", "$\n", "\n", "where $\\boldsymbol\\phi(s)$ is a one‑hot vector with `1` at the active tile.\n", "\n", "**TD update used in `ApproxQLearner.update`:**\n", "\n", "$$\n", "\\delta =\n", "\\begin{cases}\n", "r - Q(s,a) & \\text{if } s' \\text{ is terminal},\\\\[4pt]\n", "r + \\gamma \\max_{a'} Q(s',a') \\, - \\, Q(s,a) & \\text{otherwise}.\n", "\\end{cases}\n", "\\qquad\n", "\\mathbf w_a \\leftarrow \\mathbf w_a + \\alpha\\,\\delta\\,\\boldsymbol\\phi(s)\n", "$$\n", "\n", "Below is the code for the tile coder and comments highlighting\n", "where this update is implemented.\n"]}, {"cell_type": "code", "execution_count": 46, "id": "76568931", "metadata": {"id": "76568931"}, "outputs": [], "source": ["\n", "from typing import Tuple\n", "from dataclasses import dataclass\n", "import numpy as np\n", "\n", "# -------- Tile Coder (Sub‑Q1 a) ----------------------------------------- #\n", "@dataclass\n", "class TileCoder:\n", "    \"\"\"Uniform grid over (x,y) – wind dimension ignored for features.\"\"\"\n", "    nx: int\n", "    ny: int\n", "    x_range: Tuple[float, float] = (0.0, 1.0)\n", "    y_range: Tuple[float, float] = (0.0, 1.0)\n", "\n", "    def n_tiles(self) -> int:\n", "        return self.nx * self.ny\n", "\n", "    def tile_index(self, state: np.ndarray) -> int:\n", "        \"\"\"Map (x,y) to a single tile id.\"\"\"\n", "        x, y = state[0], state[1]\n", "\n", "        # Compute discrete bin indices\n", "        ix = min(int(np.floor(x * self.nx)), self.nx - 1)\n", "        iy = min(int(np.floor(y * self.ny)), self.ny - 1)\n", "\n", "        # Row‑major flatten\n", "        return iy * self.nx + ix\n"]}, {"cell_type": "markdown", "id": "63d06134", "metadata": {"id": "63d06134"}, "source": ["## **B. Sub‑question (b): Implementation (Environment & Agent)**\n", "\n", "The next two code cells provide:\n", "\n", "* `DroneWindEnv` — the windy‑field environment  \n", "* `ApproxQLearner` — the agent implementing the TD update from Section A  \n", "  *Key lines are commented as “Sub‑Q1 b”.*\n"]}, {"cell_type": "code", "execution_count": 47, "id": "968da950", "metadata": {"id": "968da950"}, "outputs": [], "source": ["\n", "import numpy as np\n", "\n", "# -------- Environment (Sub‑Q1 b) --------------------------------------- #\n", "class DroneWindEnv:\n", "    \"\"\"Continuous 2‑D field with global east–west wind.\"\"\"\n", "\n", "    def __init__(self, max_steps: int = 200, seed: int | None = None):\n", "        self.rng = np.random.default_rng(seed)\n", "        self.max_steps = max_steps\n", "        self.action_space = 4  # 0=N,1=S,2=E,3=W\n", "        self.state: np.n<PERSON><PERSON> | None = None\n", "        self._step_count = 0\n", "\n", "    # -- physics\n", "    @staticmethod\n", "    def _next_state(state: np.ndarray, action: int) -> np.ndarray:\n", "        x, y, w = state\n", "        base_move = 0.05\n", "\n", "        if action == 0:   # North\n", "            dx, dy = 0.0, base_move\n", "        elif action == 1: # South\n", "            dx, dy = 0.0, -base_move\n", "        elif action == 2: # East (head‑wind)\n", "            dx, dy = base_move * (1.0 - 0.5 * w), 0.0\n", "        elif action == 3: # <PERSON> (tail‑wind)\n", "            dx, dy = -base_move * (1.0 + 0.5 * w), 0.0\n", "        else:\n", "            raise ValueError(\"Bad action\")\n", "\n", "        new_x = np.clip(x + dx, 0.0, 1.0)\n", "        new_y = np.clip(y + dy, 0.0, 1.0)\n", "        new_w = np.clip(w + 0.01 * (np.random.rand() - 0.5), 0.0, 1.0)\n", "\n", "        return np.array([new_x, new_y, new_w], dtype=np.float32)\n", "\n", "    # -- Gym‑like API\n", "    def reset(self) -> np.ndarray:\n", "        self.state = np.array([0.05, 0.05, self.rng.random()], dtype=np.float32)\n", "        self._step_count = 0\n", "        return self.state.copy()\n", "\n", "    def step(self, action: int):\n", "        self._step_count += 1\n", "        next_state = self._next_state(self.state, action)\n", "\n", "        # +10 in charging zone (NE corner), else ‑1 per step\n", "        reward = 10.0 if (next_state[0] > 0.9 and next_state[1] > 0.9) else -1.0\n", "        done = reward == 10.0 or self._step_count >= self.max_steps\n", "\n", "        self.state = next_state\n", "        return next_state.copy(), reward, done, {}\n"]}, {"cell_type": "code", "execution_count": null, "id": "a88389ce", "metadata": {"id": "a88389ce"}, "outputs": [], "source": ["\n", "# -------- Agent (Sub‑Q1 b) --------------------------------------------- #\n", "class ApproxQLearner:\n", "    \"\"\"Linear Approximate Q‑learning with a single tiling.\"\"\"\n", "\n", "    def __init__(self,\n", "                 tile_coder: TileCoder,\n", "                 n_actions: int,\n", "                 alpha: float = 0.1,\n", "                 alpha_decay: float = 0.99,  # Add alpha decay parameter\n", "                 alpha_min: float = 0.05,     # Add minimum alpha value\n", "                 gamma: float = 0.99,\n", "                 epsilon: float = 0.1,\n", "                 epsilon_decay: float = 0.99,  # Add epsilon decay parameter\n", "                 epsilon_min: float = 0.01,     # Add minimum epsilon value\n", "                 seed: int | None = None):\n", "        self.tc = tile_coder\n", "        self.n_actions = n_actions\n", "        self.alpha = alpha\n", "        self.gamma = gamma\n", "        self.epsilon = epsilon\n", "        self.rng = np.random.default_rng(seed)\n", "        \n", "        self.alpha_decay = alpha_decay\n", "        self.alpha_min = alpha_min\n", "        self.epsilon_decay = epsilon_decay\n", "        self.epsilon_min = epsilon_min\n", "\n", "        # Weight matrix w[a, tile]\n", "        self.weights = np.zeros((n_actions, self.tc.n_tiles()), dtype=np.float32)\n", "\n", "    # -- helpers\n", "    def _phi(self, state: np.ndarray) -> int:\n", "        return self.tc.tile_index(state)\n", "\n", "    def q_values(self, state: np.ndarray) -> np.ndarray:\n", "        tid = self._phi(state)\n", "        return self.weights[:, tid]\n", "\n", "    def select_action(self, state: np.ndarray) -> int:\n", "        if self.rng.random() < self.epsilon:\n", "            return self.rng.integers(self.n_actions)\n", "        return int(np.argmax(self.q_values(state)))\n", "\n", "    # -- TD update implementing Sub‑question (a) formula\n", "    def update(self, s: np.ndarray, a: int, r: float, s_next: np.ndarray, done: bool):\n", "        tid = self._phi(s)\n", "        q_sa = self.weights[a, tid]                # current estimate\n", "\n", "        target = r if done else r + self.gamma * np.max(self.q_values(s_next))\n", "        td_error = target - q_sa                # δ\n", "        self.weights[a, tid] += self.alpha * td_error\n", "\n", "    # -- training loop\n", "    def train(self, env: DroneWindEnv, episodes: int = 1000, max_steps: int = 200):\n", "        history = []\n", "        # iterate through episodes\n", "        for episode in range(episodes):\n", "\n", "            state = env.reset()\n", "            episode_reward = 0\n", "            \n", "            # call env.step\n", "            for _ in range(max_steps):\n", "                action = self.select_action(state)\n", "                next_state, reward, done, _ = env.step(action)\n", "                \n", "                self.update(state, action, reward, next_state, done)\n", "                \n", "                state = next_state\n", "                episode_reward += reward\n", "                \n", "                if done:\n", "                    break\n", "                    \n", "            # Decay alpha and epsilon after each episode\n", "            self.alpha = max(self.alpha_min, self.alpha * self.alpha_decay)\n", "            self.epsilon = max(self.epsilon_min, self.epsilon * self.epsilon_decay)\n", "\n", "            history.append(episode_reward)\n", "\n", "        return history\n", "\n", "    # -- evaluation\n", "    def evaluate(self, env: DroneWindEnv, episodes: int = 100, max_steps: int = 200):\n", "        total = 0.0\n", "        \n", "        # iterate through episodes\n", "        for episode in range(episodes):\n", "            state = env.reset()\n", "            episode_reward = 0\n", "            \n", "            for _ in range(max_steps):\n", "                \n", "                # select best action\n", "                action = int(np.argmax(self.q_values(state)))\n", "                next_state, reward, done, _ = env.step(action)\n", "                \n", "                state = next_state\n", "                episode_reward += reward\n", "                \n", "                if done:\n", "                    break\n", "                    \n", "            total += episode_reward\n", "\n", "        return total / episodes\n"]}, {"cell_type": "markdown", "id": "1604e667", "metadata": {"id": "1604e667"}, "source": ["## **C. Sub‑question (c): Hyper‑parameter Tuning**\n", "\n", "Adjust the parameters below and re‑run training to observe their impact.\n", "Document your chosen values in the assignment write‑up (pdf file).\n"]}, {"cell_type": "code", "execution_count": null, "id": "f07d608b", "metadata": {"id": "f07d608b"}, "outputs": [], "source": ["\n", "# --- Hyper‑parameters (Sub‑Q1 c) --------------------------------------- #\n", "NX, NY = 10, 10        # tile resolution\n", "ALPHA   = 0.2          # learning rate\n", "GAMMA   = 0.95         # discount factor\n", "EPSILON = 0.2          # initial exploration rate\n", "SEED    = 42\n", "\n", "env   = DroneWindEnv(max_steps=200, seed=SEED)\n", "tc    = TileCoder(nx=NX, ny=NY)\n", "agent = ApproxQLearner(tc, n_actions=env.action_space,\n", "                       alpha=ALPHA, gamma=GAMMA,\n", "                       epsilon=EPSILON, seed=SEED)\n", "\n", "print(\"Training...\")\n", "agent.train(env, episodes=500)\n"]}, {"cell_type": "markdown", "id": "142780af", "metadata": {"id": "142780af"}, "source": ["## **D. Sub‑question (d): Performance Evaluation**\n", "\n", "Run the cell below to compute the average reward over 100 evaluation\n", "episodes and fill in the rubric table in your PDF solution.\n"]}, {"cell_type": "code", "execution_count": 52, "id": "3aa8c43e", "metadata": {"id": "3aa8c43e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Average reward over 100 evaluation episodes: -200.00\n"]}], "source": ["\n", "avg_reward = agent.evaluate(env, episodes=100)\n", "print(f\"Average reward over 100 evaluation episodes: {avg_reward:.2f}\")\n"]}, {"cell_type": "code", "execution_count": 73, "id": "1e0b8bcc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training with nx=8, ny=8, alpha=0.15, gamma=0.9, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -30.07\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.9, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -30.05\n", "Training with nx=8, ny=8, alpha=0.25, gamma=0.9, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -200.00\n", "Training with nx=8, ny=8, alpha=0.4, gamma=0.9, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -30.07\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.8, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -200.00\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.83, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -200.00\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.9, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -200.00\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.8, epsilon=0.15\n", "Average reward over 100 evaluation episodes: -30.06\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.8, epsilon=0.2\n", "Average reward over 100 evaluation episodes: -200.00\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.8, epsilon=0.25\n", "Average reward over 100 evaluation episodes: -30.07\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.8, epsilon=0.3\n", "Average reward over 100 evaluation episodes: -200.00\n", "Training with nx=8, ny=8, alpha=0.15, gamma=0.8, epsilon=0.35\n", "Average reward over 100 evaluation episodes: -29.99\n", "GREEDY SEARCH RESULTS:\n", "Best tile resolution: 8\n", "Best alpha: 0.15\n", "Best gamma: 0.8\n", "Best epsilon: 0.35\n", "Time taken: 296.43 seconds\n"]}], "source": ["import time\n", "# --- Hyper‑parameters (Sub‑Q1 c) --------------------------------------- #\n", "NX, NY = 8, 8        # tile resolution\n", "ALPHA   = 0.15          # learning rate\n", "GAMMA   = 0.9         # discount factor\n", "EPSILON = 0.25          # initial exploration rate\n", "SEED    = 42\n", "\n", "tile_resolutions = [8] # 5 values to try\n", "alpha_values = [0.15, 0.25, 0.4] # 5 values to try\n", "gamma_values = [0.8, 0.83, 0.9] # 5 values to try\n", "epsilon_values = [0.15, 0.2, 0.25, 0.3, 0.35] # 5 values to try\n", "\n", "start_time = time.time()\n", "\n", "best_tile_resolution = None\n", "best_avg_reward = -float('inf')\n", "\n", "for tile_resolution in tile_resolutions:\n", "    nx = ny = tile_resolution\n", "    \n", "    alpha = ALPHA\n", "    gamma = GAMMA\n", "    epsilon = EPSILON\n", "\n", "    env   = DroneWindEnv(max_steps=200, seed=SEED)\n", "    tc    = TileCoder(nx=nx, ny=ny)\n", "    agent = ApproxQLearner(tc, n_actions=env.action_space,\n", "                            alpha=alpha, gamma=gamma,\n", "                            epsilon=epsilon, seed=SEED)\n", "\n", "    print(f\"Training with nx={nx}, ny={ny}, alpha={alpha}, gamma={gamma}, epsilon={epsilon}\")\n", "    agent.train(env, episodes=500)\n", "\n", "    avg_reward = agent.evaluate(env, episodes=100)\n", "    print(f\"Average reward over 100 evaluation episodes: {avg_reward:.2f}\")\n", "    \n", "    if avg_reward > best_avg_reward:\n", "        best_tile_resolution = tile_resolution\n", "        best_avg_reward = avg_reward\n", "\n", "best_alpha = None\n", "best_avg_reward = -float('inf')\n", "\n", "for alpha in alpha_values:\n", "    nx = ny = best_tile_resolution\n", "    gamma = GAMMA\n", "    epsilon = EPSILON\n", "    \n", "    env   = DroneWindEnv(max_steps=200, seed=SEED)\n", "    tc    = TileCoder(nx=nx, ny=ny)\n", "    agent = ApproxQLearner(tc, n_actions=env.action_space,\n", "                            alpha=alpha, gamma=gamma,\n", "                            epsilon=epsilon, seed=SEED)\n", "\n", "    print(f\"Training with nx={nx}, ny={ny}, alpha={alpha}, gamma={gamma}, epsilon={epsilon}\")\n", "    agent.train(env, episodes=500)\n", "\n", "    avg_reward = agent.evaluate(env, episodes=100)\n", "    print(f\"Average reward over 100 evaluation episodes: {avg_reward:.2f}\")\n", "    \n", "    if avg_reward > best_avg_reward:\n", "        best_alpha = alpha\n", "        best_avg_reward = avg_reward\n", "\n", "best_gamma = None\n", "best_avg_reward = -float('inf')\n", "\n", "for gamma in gamma_values:\n", "    nx = ny = best_tile_resolution\n", "    alpha = best_alpha\n", "    epsilon = EPSILON\n", "    \n", "    env   = DroneWindEnv(max_steps=200, seed=SEED)\n", "    tc    = TileCoder(nx=nx, ny=ny)\n", "    agent = ApproxQLearner(tc, n_actions=env.action_space,\n", "                            alpha=alpha, gamma=gamma,\n", "                            epsilon=epsilon, seed=SEED)\n", "    \n", "    print(f\"Training with nx={nx}, ny={ny}, alpha={alpha}, gamma={gamma}, epsilon={epsilon}\")\n", "    agent.train(env, episodes=500)\n", "\n", "    avg_reward = agent.evaluate(env, episodes=100)\n", "    print(f\"Average reward over 100 evaluation episodes: {avg_reward:.2f}\")\n", "    \n", "    if avg_reward > best_avg_reward:\n", "        best_gamma = gamma\n", "        best_avg_reward = avg_reward\n", "\n", "best_epsilon = None\n", "best_avg_reward = -float('inf')\n", "\n", "for epsilon in epsilon_values:\n", "    nx = ny = best_tile_resolution\n", "    alpha = best_alpha\n", "    gamma = best_gamma\n", "    \n", "    env   = DroneWindEnv(max_steps=200, seed=SEED)\n", "    tc    = TileCoder(nx=nx, ny=ny)\n", "    agent = ApproxQLearner(tc, n_actions=env.action_space,\n", "                            alpha=alpha, gamma=gamma,\n", "                            epsilon=epsilon, seed=SEED)\n", "\n", "    print(f\"Training with nx={nx}, ny={ny}, alpha={alpha}, gamma={gamma}, epsilon={epsilon}\")\n", "    agent.train(env, episodes=500)\n", "\n", "\n", "    avg_reward = agent.evaluate(env, episodes=100)\n", "    print(f\"Average reward over 100 evaluation episodes: {avg_reward:.2f}\")\n", "    \n", "    if avg_reward > best_avg_reward:\n", "        best_epsilon = epsilon\n", "        best_avg_reward = avg_reward\n", "\n", "\n", "print(\"GREEDY SEARCH RESULTS:\")\n", "print(f\"Best tile resolution: {best_tile_resolution}\")\n", "print(f\"Best alpha: {best_alpha}\")\n", "print(f\"Best gamma: {best_gamma}\")\n", "print(f\"Best epsilon: {best_epsilon}\")\n", "\n", "\n", "print(f\"Time taken: {time.time() - start_time:.2f} seconds\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "4ddeb975", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training with nx=8, ny=8, alpha=0.1, gamma=0.8, epsilon=0.15\n", "Time taken: 16.23 seconds\n", "Average reward over 100 evaluation episodes: -30.00\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.8, epsilon=0.2\n", "Time taken: 15.76 seconds\n", "Average reward over 100 evaluation episodes: -30.01\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.8, epsilon=0.3\n", "Time taken: 15.78 seconds\n", "Average reward over 100 evaluation episodes: -30.06\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.9, epsilon=0.15\n", "Time taken: 15.95 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.9, epsilon=0.2\n", "Time taken: 16.47 seconds\n", "Average reward over 100 evaluation episodes: -30.07\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.9, epsilon=0.3\n", "Time taken: 15.02 seconds\n", "Average reward over 100 evaluation episodes: -30.07\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.95, epsilon=0.15\n", "Time taken: 14.85 seconds\n", "Average reward over 100 evaluation episodes: -30.08\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.95, epsilon=0.2\n", "Time taken: 14.12 seconds\n", "Average reward over 100 evaluation episodes: -30.02\n", "\n", "Training with nx=8, ny=8, alpha=0.1, gamma=0.95, epsilon=0.3\n", "Time taken: 15.09 seconds\n", "Average reward over 100 evaluation episodes: -30.10\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.8, epsilon=0.15\n", "Time taken: 14.01 seconds\n", "Average reward over 100 evaluation episodes: -30.11\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.8, epsilon=0.2\n", "Time taken: 15.38 seconds\n", "Average reward over 100 evaluation episodes: -200.00\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.8, epsilon=0.3\n", "Time taken: 13.60 seconds\n", "Average reward over 100 evaluation episodes: -148.46\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.9, epsilon=0.15\n", "Time taken: 12.23 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.9, epsilon=0.2\n", "Time taken: 12.30 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.9, epsilon=0.3\n", "Time taken: 13.39 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.95, epsilon=0.15\n", "Time taken: 14.94 seconds\n", "Average reward over 100 evaluation episodes: -30.03\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.95, epsilon=0.2\n", "Time taken: 12.45 seconds\n", "Average reward over 100 evaluation episodes: -29.99\n", "\n", "Training with nx=8, ny=8, alpha=0.2, gamma=0.95, epsilon=0.3\n", "Time taken: 13.14 seconds\n", "Average reward over 100 evaluation episodes: -30.07\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.8, epsilon=0.15\n", "Time taken: 11.90 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.8, epsilon=0.2\n", "Time taken: 14.52 seconds\n", "Average reward over 100 evaluation episodes: -30.04\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.8, epsilon=0.3\n", "Time taken: 15.44 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.9, epsilon=0.15\n", "Time taken: 12.80 seconds\n", "Average reward over 100 evaluation episodes: -30.05\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.9, epsilon=0.2\n", "Time taken: 12.19 seconds\n", "Average reward over 100 evaluation episodes: -30.04\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.9, epsilon=0.3\n", "Time taken: 13.68 seconds\n", "Average reward over 100 evaluation episodes: -30.03\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.95, epsilon=0.15\n", "Time taken: 11.09 seconds\n", "Average reward over 100 evaluation episodes: -32.03\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.95, epsilon=0.2\n", "Time taken: 12.04 seconds\n", "Average reward over 100 evaluation episodes: -30.04\n", "\n", "Training with nx=8, ny=8, alpha=0.3, gamma=0.95, epsilon=0.3\n", "Time taken: 13.32 seconds\n", "Average reward over 100 evaluation episodes: -30.04\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.8, epsilon=0.15\n", "Time taken: 17.63 seconds\n", "Average reward over 100 evaluation episodes: -30.06\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.8, epsilon=0.2\n", "Time taken: 17.16 seconds\n", "Average reward over 100 evaluation episodes: -30.11\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.8, epsilon=0.3\n", "Time taken: 17.75 seconds\n", "Average reward over 100 evaluation episodes: -30.07\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.9, epsilon=0.15\n", "Time taken: 15.68 seconds\n", "Average reward over 100 evaluation episodes: -30.06\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.9, epsilon=0.2\n", "Time taken: 15.77 seconds\n", "Average reward over 100 evaluation episodes: -30.07\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.9, epsilon=0.3\n", "Time taken: 13.61 seconds\n", "Average reward over 100 evaluation episodes: -30.06\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.95, epsilon=0.15\n", "Time taken: 15.91 seconds\n", "Average reward over 100 evaluation episodes: -30.03\n", "\n", "Training with nx=8, ny=10, alpha=0.1, gamma=0.95, epsilon=0.2\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[56], line 27\u001b[0m\n\u001b[0;32m     22\u001b[0m agent \u001b[38;5;241m=\u001b[39m Approx<PERSON>earner(tc, n_actions\u001b[38;5;241m=\u001b[39menv\u001b[38;5;241m.\u001b[39maction_space,\n\u001b[0;32m     23\u001b[0m                        alpha\u001b[38;5;241m=\u001b[39malpha, gamma\u001b[38;5;241m=\u001b[39mgamma,\n\u001b[0;32m     24\u001b[0m                        epsilon\u001b[38;5;241m=\u001b[39mepsilon, seed\u001b[38;5;241m=\u001b[39mSEED)\n\u001b[0;32m     26\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTraining with nx=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnx\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, ny=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mny\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, alpha=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00<PERSON>pha\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, gamma=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mgamma\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m, epsilon=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mepsilon\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m---> 27\u001b[0m agent\u001b[38;5;241m.\u001b[39mtrain(env, episodes\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m500\u001b[39m)\n\u001b[0;32m     29\u001b[0m end_time \u001b[38;5;241m=\u001b[39m time\u001b[38;5;241m.\u001b[39mtime()\n\u001b[0;32m     30\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTime taken: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mend_time\u001b[38;5;250m \u001b[39m\u001b[38;5;241m-\u001b[39m\u001b[38;5;250m \u001b[39mstart_time\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.2f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m seconds\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n", "Cell \u001b[1;32mIn[54], line 56\u001b[0m, in \u001b[0;36mApproxQLearner.train\u001b[1;34m(self, env, episodes, max_steps)\u001b[0m\n\u001b[0;32m     54\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m _ \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(max_steps):\n\u001b[0;32m     55\u001b[0m     action \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mselect_action(state)\n\u001b[1;32m---> 56\u001b[0m     next_state, reward, done, _ \u001b[38;5;241m=\u001b[39m env\u001b[38;5;241m.\u001b[39mstep(action)\n\u001b[0;32m     58\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mupdate(state, action, reward, next_state, done)\n\u001b[0;32m     60\u001b[0m     state \u001b[38;5;241m=\u001b[39m next_state\n", "Cell \u001b[1;32mIn[47], line 52\u001b[0m, in \u001b[0;36mDroneWindEnv.step\u001b[1;34m(self, action)\u001b[0m\n\u001b[0;32m     49\u001b[0m done \u001b[38;5;241m=\u001b[39m reward \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m10.0\u001b[39m \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_step_count \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmax_steps\n\u001b[0;32m     51\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstate \u001b[38;5;241m=\u001b[39m next_state\n\u001b[1;32m---> 52\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m next_state\u001b[38;5;241m.\u001b[39mcopy(), reward, done, {}\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["import time\n", "# --- Hyper‑parameters (Sub‑Q1 c) --------------------------------------- #\n", "NX, NY = 10, 10        # tile resolution\n", "ALPHA   = 0.2          # learning rate\n", "GAMMA   = 0.95         # discount factor\n", "EPSILON = 0.2          # initial exploration rate\n", "SEED    = 42\n", "\n", "tile_resolutions = [10, 20, 25] # 5 values to try\n", "alpha_values = [0.1, 0.2, 0.3] # 3 values to try\n", "gamma_values = [0.8, 0.9, 0.95] # 3 values to try\n", "epsilon_values = [0.15, 0.2, 0.3] # 3 values to try\n", "\n", "for tile_resolution in tile_resolutions:\n", "    for alpha in alpha_values:\n", "        for gamma in gamma_values:\n", "            for epsilon in epsilon_values:\n", "                start_time = time.time()\n", "                env   = DroneWindEnv(max_steps=200, seed=SEED)\n", "                tc    = TileCoder(nx=tile_resolution, ny=tile_resolution)\n", "                agent = ApproxQLearner(tc, n_actions=env.action_space,\n", "                                        alpha=alpha, gamma=gamma,\n", "                                        epsilon=epsilon, seed=SEED)\n", "\n", "                print(f\"Training with nx={nx}, ny={ny}, alpha={alpha}, gamma={gamma}, epsilon={epsilon}\")\n", "                agent.train(env, episodes=500)\n", "\n", "                end_time = time.time()\n", "                print(f\"Time taken: {end_time - start_time:.2f} seconds\")\n", "\n", "                avg_reward = agent.evaluate(env, episodes=100)\n", "                print(f\"Average reward over 100 evaluation episodes: {avg_reward:.2f}\")\n", "                print()\n", "\n", "\n"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}