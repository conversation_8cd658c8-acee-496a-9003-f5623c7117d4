{"cells": [{"cell_type": "markdown", "metadata": {"id": "aHAJb4xguBSH"}, "source": ["# Assignment 2 – Question 2 Notebook\n", "**Deep Q-Learning for Pacman**\n", "\n", "This notebook implements a DQN agent for playing Pac<PERSON>. The code is organized into distinct sections\n", "that map to the assignment sub-questions:\n", "\n", "| Sub-question | Description | Where to Modify |\n", "|--------------|-------------|-----------------|\n", "| (a) Improve Algorithm | Enhance training speed through algorithmic improvements | **Cell 3 (DQN Core Algorithm)**: Modify `ReplayBuffer`, `DQNAgent.learn()`, loss functions<br>**Cell 4 (Training Strategy)**: Modify learning frequency, warm-up, frame stacking |\n", "| (b) Hyperparameter Tuning | Fine-tune at least 2 hyperparameters | **Cell 1 (Configuration)**: All hyperparameters are centralized here |\n", "| (c) Performance Analysis | Evaluate and compare results | **Cell 5 (Testing)**: Run evaluation and collect metrics |\n", "\n", "## Code Structure Overview:\n", "1. **Cell 1**: Configuration - All hyperparameters and reward values **MODIFY for sub-question (b)**\n", "2. **Cell 2**: Game Environment - **DO NOT MODIFY** (just run and skip)\n", "3. **Cell 3**: DQN Core Algorithm - **MODIFY for sub-question (a)** if you want to improve algorithm\n", "4. **Cell 4**: Training Loop - **MODIFY for sub-question (a)** if you want to improve training strategy\n", "5. **Cell 5**: Testing & Evaluation - **MODIFY for sub-question (c)**"]}, {"cell_type": "markdown", "metadata": {"id": "cHplquk5uBSL"}, "source": ["### Cell 1: Configuration (Hyperparameters & Rewards)\n", "\n", "**For sub-question (b)**: Tune hyperparameters here.\n", "\n", "**For sub-question (a)**: You'll modify the algorithm implementation in Cells 3 & 4, not here."]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "iykgOQfBuBSL"}, "outputs": [], "source": ["# ===== REWARD VALUES =====\n", "# These define the reward structure of the game\n", "STEP_PENALTY = -0.1  # Penalty for each time step\n", "WALL_PENALTY = -1  # Additional penalty for hitting walls\n", "PELLET_REWARD = 10  # Reward for eating a normal pellet\n", "POWER_PELLET_REWARD = 50  # Reward for eating a power pellet\n", "GHOST_REWARD = 200  # Reward for eating a ghost (when frightened)\n", "DEATH_PENALTY = -100  # Penalty for being caught by a ghost\n", "VICTORY_REWARD = 200  # Reward for clearing all pellets\n", "\n", "# ===== HYPERPARAMETERS (Sub-question b: Tune these) =====\n", "# Exploration parameters\n", "EPSILON_START = 1.0  # Initial exploration rate\n", "EPSILON_END = 0.01  # Final exploration rate\n", "EPSILON_DECAY = 0.999  # Decay rate per episode\n", "\n", "# Learning parameters\n", "LEARNING_RATE = 0.00025  # Neural network learning rate\n", "BATCH_SIZE = 64  # Batch size for training\n", "GAMMA = 0.99  # Discount factor for future rewards\n", "\n", "# Network update parameters\n", "TARGET_UPDATE_STEPS = 1000  # Steps between target network updates\n", "\n", "# Training duration\n", "NUM_EPISODES = 1000  # Total training episodes\n", "\n", "# ===== DEFAULT ALGORITHM SETTINGS =====\n", "# These are baseline settings. For sub-question (a), you'll modify\n", "# the actual implementation in Cells 3 & 4, not just these values.\n", "FRAME_STACK_SIZE = 4  # Number of frames to stack\n", "REPLAY_BUFFER_CAPACITY = 100000  # Size of experience replay buffer\n", "LEARN_EVERY_N_STEPS = 2  # How often to perform learning update\n", "\n", "# ===== GAME PARAMETERS (Do not modify) =====\n", "SCREEN_WIDTH, SCREEN_HEIGHT = 280, 316\n", "GRID_SIZE = 28\n", "FRIGHTENED_DURATION = 10"]}, {"cell_type": "markdown", "metadata": {"id": "WMBguJJLuBSM"}, "source": ["### Cell 2: Game UI & Environment\n", "\n", "This cell contains the Pacman game environment.\n", "**DO NOT MODIFY** - Just run this cell and skip to Cell 3."]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "dcHz7D_GuBSN", "outputId": "bcb28c3d-7cc8-4c16-ed89-5fe9be9bac27"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["pygame 2.6.1 (SDL 2.28.4, Python 3.11.13)\n", "Hello from the pygame community. https://www.pygame.org/contribute.html\n"]}], "source": ["import math\n", "import random\n", "\n", "import numpy as np\n", "import pygame\n", "\n", "# --- Color Definitions ---\n", "BLACK = (0, 0, 0)\n", "WHITE = (255, 255, 255)\n", "YELLOW = (255, 255, 0)\n", "BLUE = (0, 0, 255)\n", "RED = (255, 0, 0)\n", "PINK = (255, 182, 193)\n", "CYAN = (0, 255, 255)\n", "ORANGE = (255, 165, 0)\n", "GRAY = (128, 128, 128)\n", "\n", "\n", "class PacmanGame:\n", "    \"\"\"\n", "    Environment class that encapsulates Pacman game logic and rendering.\n", "    All reward values are defined in Cell 1.\n", "    \"\"\"\n", "\n", "    def __init__(self):\n", "        pygame.init()\n", "        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))\n", "        pygame.display.set_caption(\"DQN Pac-Man\")\n", "        self.clock = pygame.time.Clock()\n", "\n", "        self.font = pygame.font.Font(None, 20)\n", "        # Game map (1: wall, 0: empty, 2: pellet, 3: power pellet)\n", "        self.level_map = [\n", "            \"1111111111\",\n", "            \"1223222221\",\n", "            \"1211221121\",\n", "            \"1222222221\",\n", "            \"1211221121\",\n", "            \"1222222221\",\n", "            \"1111111111\",\n", "        ]\n", "        self.width = len(self.level_map[0])\n", "        self.height = len(self.level_map)\n", "        self.action_space = 4  # 0:Up, 1:<PERSON>, 2:Left, 3:Right\n", "        self.training_info = {}\n", "        self.hit_wall = False\n", "        self.frightened_timer = 0\n", "        self.frightened_duration = FRIGHTENED_DURATION\n", "        self.reset()\n", "\n", "    def reset(self):\n", "        \"\"\"Reset game state to the initial configuration.\"\"\"\n", "        self.pacman_pos = [1, 3]  # Pacman starting position\n", "        self.pacman_direction = (\n", "            3  # <PERSON><PERSON> facing direction (0:Up, 1:Down, 2:Left, 3:Right)\n", "        )\n", "        self.ghosts = {\n", "            \"blinky\": {\"pos\": [1, 1], \"color\": RED, \"direction\": 3},\n", "            \"pinky\": {\"pos\": [8, 5], \"color\": PINK, \"direction\": 2},\n", "        }\n", "        self.score = 0\n", "        self.pellets = []\n", "        self.power_pellets = []\n", "        self.walls = []\n", "        for r, row in enumerate(self.level_map):\n", "            for c, char in enumerate(row):\n", "                if char == \"1\":\n", "                    self.walls.append(\n", "                        pygame.Rect(\n", "                            c * GRID_SIZE, r * GRID_SIZE, GRID_SIZE, GRID_SIZE\n", "                        )\n", "                    )\n", "                elif char == \"2\":\n", "                    self.pellets.append(\n", "                        pygame.Rect(\n", "                            c * GRID_SIZE + GRID_SIZE // 2,\n", "                            r * GRID_SIZE + GRID_SIZE // 2,\n", "                            4,\n", "                            4,\n", "                        )\n", "                    )\n", "                elif char == \"3\":\n", "                    self.power_pellets.append(\n", "                        pygame.Rect(\n", "                            c * GRID_SIZE + GRID_SIZE // 2,\n", "                            r * GRID_SIZE + GRID_SIZE // 2,\n", "                            8,\n", "                            8,\n", "                        )\n", "                    )\n", "        self.done = False\n", "        self.hit_wall = False\n", "        self.frightened_timer = 0\n", "        self._render_game_state()  # Initial render\n", "        return self._get_state()\n", "\n", "    def _get_state(self):\n", "        \"\"\"Get current game screen pixels as state.\"\"\"\n", "        frame = pygame.surfarray.array3d(self.screen)\n", "        # Convert to grayscale to reduce state dimensions\n", "        frame = np.dot(frame[..., :3], [0.2989, 0.5870, 0.1140])\n", "        frame = frame.T  # Transpose to match (Height, Width)\n", "        frame = frame.astype(np.uint8)\n", "        # Add channel dimension to match CNN input format (C, H, W)\n", "        frame = np.expand_dims(frame, axis=0)\n", "        return frame\n", "\n", "    def _move(self, pos, direction):\n", "        \"\"\"Move position based on direction, with screen wrapping.\"\"\"\n", "        if direction == 0:\n", "            pos[1] -= 1  # Up\n", "        elif direction == 1:\n", "            pos[1] += 1  # Down\n", "        elif direction == 2:\n", "            pos[0] -= 1  # Left\n", "        elif direction == 3:\n", "            pos[0] += 1  # Right\n", "        # Handle wrapping around the screen\n", "        if pos[0] < 0:\n", "            pos[0] = self.width - 1\n", "        if pos[0] >= self.width:\n", "            pos[0] = 0\n", "        return pos\n", "\n", "    def _check_collision(self, pos):\n", "        \"\"\"Check if a given grid position collides with a wall.\"\"\"\n", "        rect = pygame.Rect(\n", "            pos[0] * GRID_SIZE, pos[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE\n", "        )\n", "        for wall in self.walls:\n", "            if rect.colliderect(wall):\n", "                return True\n", "        return False\n", "\n", "    def _get_valid_moves(self, pos):\n", "        \"\"\"Get all valid (non-wall) move directions from a given position.\"\"\"\n", "        valid_moves = []\n", "        for direction in range(4):  # 0:Up, 1:Down, 2:Left, 3:Right\n", "            potential_pos = self._move(list(pos), direction)\n", "            if not self._check_collision(potential_pos):\n", "                valid_moves.append(direction)\n", "        return valid_moves\n", "\n", "    def step(self, action):\n", "        \"\"\"Execute one action step, return next_state, reward, done.\"\"\"\n", "        # Use reward values from Cell 1\n", "        reward = STEP_PENALTY\n", "\n", "        # Decrement frightened timer if active\n", "        if self.frightened_timer > 0:\n", "            self.frightened_timer -= 1\n", "\n", "        # Move Pacman\n", "        new_pos = list(self.pacman_pos)\n", "        potential_pos = self._move(list(new_pos), action)\n", "        if not self._check_collision(potential_pos):\n", "            self.pacman_pos = potential_pos\n", "            self.pacman_direction = action  # Update <PERSON><PERSON>'s facing direction\n", "            self.hit_wall = False\n", "        else:\n", "            self.hit_wall = True\n", "            reward += WALL_PENALTY\n", "\n", "        pacman_rect = pygame.Rect(\n", "            self.pacman_pos[0] * GRID_SIZE,\n", "            self.pacman_pos[1] * GRID_SIZE,\n", "            GRID_SIZE,\n", "            GRID_SIZE,\n", "        )\n", "\n", "        # Check rewards from eating pellets\n", "        eaten_pellet = pacman_rect.collidelist(self.pellets)\n", "        if eaten_pellet != -1:\n", "            self.pellets.pop(eaten_pellet)\n", "            self.score += PELLET_REWARD\n", "            reward += PELLET_REWARD\n", "\n", "        eaten_power_pellet = pacman_rect.collidelist(self.power_pellets)\n", "        if eaten_power_pellet != -1:\n", "            self.power_pellets.pop(eaten_power_pellet)\n", "            self.score += POWER_PELLET_REWARD\n", "            reward += POWER_PELLET_REWARD\n", "            # Activate frightened state for ghosts\n", "            self.frightened_timer = self.frightened_duration\n", "\n", "        # Collision detection with ghosts (check before ghosts move)\n", "        for g in self.ghosts.values():\n", "            ghost_rect = pygame.Rect(\n", "                g[\"pos\"][0] * GRID_SIZE,\n", "                g[\"pos\"][1] * GRID_SIZE,\n", "                GRID_SIZE,\n", "                GRID_SIZE,\n", "            )\n", "            if pacman_rect.colliderect(ghost_rect):\n", "                if self.frightened_timer > 0:\n", "                    self.score += GHOST_REWARD\n", "                    reward += GHOST_REWARD\n", "                    g[\"pos\"] = [1, 1]  # Send eaten ghost back to start\n", "                else:\n", "                    self.done = True\n", "                    reward = DEATH_PENALTY\n", "                    self._render_game_state()\n", "                    return self._get_state(), reward, self.done\n", "\n", "        # Move ghosts\n", "        for g in self.ghosts.values():\n", "            valid_moves = self._get_valid_moves(g[\"pos\"])\n", "            # Avoid immediate reversal unless there is no other choice\n", "            if len(valid_moves) > 1:\n", "                reverse_map = {0: 1, 1: 0, 2: 3, 3: 2}\n", "                reverse_direction = reverse_map.get(g[\"direction\"])\n", "                if reverse_direction in valid_moves:\n", "                    valid_moves.remove(reverse_direction)\n", "\n", "            if valid_moves:\n", "                ghost_action = random.choice(valid_moves)\n", "                g[\"pos\"] = self._move(list(g[\"pos\"]), ghost_action)\n", "                g[\"direction\"] = ghost_action\n", "\n", "        # Check game over conditions (caught by a ghost or all pellets eaten)\n", "        for g in self.ghosts.values():\n", "            ghost_rect = pygame.Rect(\n", "                g[\"pos\"][0] * GRID_SIZE,\n", "                g[\"pos\"][1] * GRID_SIZE,\n", "                GRID_SIZE,\n", "                GRID_SIZE,\n", "            )\n", "            if pacman_rect.colliderect(ghost_rect):\n", "                if self.frightened_timer > 0:\n", "                    self.score += GHOST_REWARD\n", "                    reward += GHOST_REWARD\n", "                    g[\"pos\"] = [1, 1]  # Send eaten ghost back to start\n", "                else:\n", "                    self.done = True\n", "                    reward = DEATH_PENALTY\n", "                    break\n", "\n", "        if not self.pellets and not self.power_pellets:\n", "            self.done = True\n", "            reward = VICTORY_REWARD\n", "\n", "        # Render the game state and get the new state\n", "        self._render_game_state()\n", "        next_state = self._get_state()\n", "\n", "        return next_state, reward, self.done\n", "\n", "    def _render_game_state(self):\n", "        \"\"\"Render the game world to the screen surface without updating the display.\"\"\"\n", "        self.screen.fill(BLACK)\n", "        for wall in self.walls:\n", "            pygame.draw.rect(self.screen, BLUE, wall)\n", "        for pellet in self.pellets:\n", "            pygame.draw.rect(self.screen, WHITE, pellet)\n", "        for p_pellet in self.power_pellets:\n", "            pygame.draw.rect(self.screen, WHITE, p_pellet)\n", "\n", "        self._draw_pacman()\n", "        for g in self.ghosts.values():\n", "            self._draw_ghost(g[\"pos\"], g[\"color\"])\n", "        score_text = self.font.render(f\"Score: {self.score}\", True, WHITE)\n", "        self.screen.blit(score_text, (10, 10))\n", "\n", "    def _draw_pacman(self):\n", "        \"\"\"Draw Pacman with its mouth oriented based on its direction.\"\"\"\n", "        pos_x = self.pacman_pos[0] * GRID_SIZE + GRID_SIZE // 2\n", "        pos_y = self.pacman_pos[1] * GRID_SIZE + GRID_SIZE // 2\n", "        radius = GRID_SIZE // 2\n", "        center = (pos_x, pos_y)\n", "\n", "        # Define mouth angles for each direction\n", "        angles = {\n", "            0: (math.pi / 4, 3 * math.pi / 4),  # Up\n", "            1: (5 * math.pi / 4, 7 * math.pi / 4),  # Down\n", "            2: (3 * math.pi / 4, 5 * math.pi / 4),  # Left\n", "            3: (-math.pi / 4, math.pi / 4),  # Right\n", "        }\n", "\n", "        # Correct for Pygame's inverted Y-axis. The visual representation for \"up\"\n", "        # and \"down\" movement needs to be swapped.\n", "        visual_correction_angles = {\n", "            0: angles[1],\n", "            1: angles[0],\n", "            2: angles[2],\n", "            3: angles[3],\n", "        }\n", "        start_angle, end_angle = visual_correction_angles[self.pacman_direction]\n", "\n", "        # Draw body and mouth\n", "        pygame.draw.circle(self.screen, YELLOW, center, radius)\n", "        points = [center]\n", "        for n in range(16):\n", "            theta = start_angle + (end_angle - start_angle) * n / 15\n", "            points.append(\n", "                (\n", "                    center[0] + radius * math.cos(theta),\n", "                    center[1] + radius * math.sin(theta),\n", "                )\n", "            )\n", "        pygame.draw.polygon(self.screen, BLACK, points)\n", "\n", "    def _draw_ghost(self, pos, color):\n", "        \"\"\"Draw a ghost shape.\"\"\"\n", "        # Change ghost color if in frightened state\n", "        current_color = GRAY if self.frightened_timer > 0 else color\n", "\n", "        pos_x = pos[0] * GRID_SIZE\n", "        pos_y = pos[1] * GRID_SIZE\n", "        radius = GRID_SIZE // 2\n", "        body_rect = pygame.Rect(pos_x, pos_y + radius, GRID_SIZE, radius)\n", "\n", "        # Body\n", "        pygame.draw.rect(\n", "            self.screen,\n", "            current_color,\n", "            body_rect,\n", "            border_bottom_left_radius=3,\n", "            border_bottom_right_radius=3,\n", "        )\n", "        # Head\n", "        pygame.draw.circle(\n", "            self.screen, current_color, (pos_x + radius, pos_y + radius), radius\n", "        )\n", "        # Eyes\n", "        eye_radius = GRID_SIZE // 8\n", "        pupil_radius = GRID_SIZE // 16\n", "        pygame.draw.circle(\n", "            self.screen, WHITE, (pos_x + radius - 5, pos_y + radius - 2), eye_radius\n", "        )\n", "        pygame.draw.circle(\n", "            self.screen,\n", "            BLACK,\n", "            (pos_x + radius - 5, pos_y + radius - 2),\n", "            pupil_radius,\n", "        )\n", "        pygame.draw.circle(\n", "            self.screen, WHITE, (pos_x + radius + 5, pos_y + radius - 2), eye_radius\n", "        )\n", "        pygame.draw.circle(\n", "            self.screen,\n", "            BLACK,\n", "            (pos_x + radius + 5, pos_y + radius - 2),\n", "            pupil_radius,\n", "        )\n", "\n", "    def render(self, training_info={}):\n", "        \"\"\"Render the complete game interface, including training info.\"\"\"\n", "        # Clear bottom info area to prevent old text from lingering\n", "        info_area_rect = pygame.Rect(\n", "            0,\n", "            self.height * GRID_SIZE,\n", "            SCREEN_WIDTH,\n", "            SCREEN_HEIGHT - self.height * GRID_SIZE,\n", "        )\n", "        self.screen.fill(BLAC<PERSON>, info_area_rect)\n", "\n", "        # Separate warning messages from regular info for display\n", "        info_to_render = dict(training_info)\n", "        warn_message = info_to_render.pop(\"WARN\", None)\n", "\n", "        # Display training info\n", "        if info_to_render:\n", "            items = list(info_to_render.items())\n", "            mid_point = (len(items) + 1) // 2\n", "            # First column\n", "            y_offset = self.height * GRID_SIZE + 10\n", "            for key, value in items[:mid_point]:\n", "                info_text = self.font.render(f\"{key}: {value}\", True, WHITE)\n", "                self.screen.blit(info_text, (10, y_offset))\n", "                y_offset += 28\n", "            # Second column\n", "            y_offset = self.height * GRID_SIZE + 10\n", "            x_offset = SCREEN_WIDTH // 2\n", "            for key, value in items[mid_point:]:\n", "                info_text = self.font.render(f\"{key}: {value}\", True, WHITE)\n", "                self.screen.blit(info_text, (x_offset, y_offset))\n", "                y_offset += 28\n", "\n", "        # Render warning message at the bottom\n", "        if warn_message:\n", "            warn_text = self.font.render(f\"Status: {warn_message}\", True, YELLOW)\n", "            warn_rect = warn_text.get_rect(bottomleft=(10, SCREEN_HEIGHT - 10))\n", "            self.screen.blit(warn_text, warn_rect)\n", "\n", "        # Update the full display\n", "        pygame.display.flip()\n", "        if self.clock:\n", "            self.clock.tick(30)  # Control game frame rate"]}, {"cell_type": "markdown", "metadata": {"id": "zaIs9iQNuBSP"}, "source": ["### Cell 3: DQN Core Algorithm\n", "\n", "**For sub-question (a)**: MODIFY THIS CELL to improve the **algorithm**.\n", "\n", "Possible improvements to consider:\n", "- Implement a warm-up phase in ReplayBuffer before starting to learn\n", "- Modify the loss function (MSE vs Huber)\n", "- Implement prioritized experience replay\n", "- Add reward shaping in the agent\n", "- Modify the network architecture\n", "- Implement Double DQN or Dueling DQN\n", "\n", "**You are also ENCOURAGED TO TRY YOUR OWN IDEAS**"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "HtmYfMFYuBSP"}, "outputs": [], "source": ["from collections import deque\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import torch.optim as optim\n", "\n", "# <--- 新增：定义一个全局的 device 变量，方便 learn 方法访问\n", "# <--- 我们将在训练循环开始时为它赋值\n", "device = torch.device(\"cpu\")\n", "\n", "class ReplayBuffer:\n", "    \"\"\"Experience replay buffer for DQN.\n", "\n", "    Sub-question (a): Consider implementing improvements such as:\n", "    - Warm-up phase: Don't start learning until buffer has minimum samples\n", "    - Prioritized replay: Sample important experiences more frequently\n", "    \"\"\"\n", "\n", "    def __init__(self, capacity):\n", "        self.buffer = deque(maxlen=capacity)\n", "\n", "    def push(self, state, action, reward, next_state, done):\n", "        \"\"\"Store one experience tuple.\"\"\"\n", "        # 注意：这里的数据暂时都存储在CPU上\n", "        state = torch.tensor(state, dtype=torch.float32)\n", "        next_state = torch.tensor(next_state, dtype=torch.float32)\n", "        action = torch.tensor([action], dtype=torch.int64)\n", "        reward = torch.tensor([reward], dtype=torch.float32)\n", "        done = torch.tensor([done], dtype=torch.bool)\n", "        self.buffer.append((state, action, reward, next_state, done))\n", "\n", "    def sample(self, batch_size):\n", "        \"\"\"Randomly sample a batch of experiences.\"\"\"\n", "        state, action, reward, next_state, done = zip(\n", "            *random.sample(self.buffer, batch_size)\n", "        )\n", "        return (\n", "            torch.stack(list(state)),\n", "            torch.cat(action),\n", "            torch.cat(reward),\n", "            torch.stack(list(next_state)),\n", "            torch.cat(done),\n", "        )\n", "\n", "    def __len__(self):\n", "        return len(self.buffer)  # this method could help warm-up\n", "\n", "\n", "class QNetwork(nn.Module):\n", "    \"\"\"Neural network for approximating Q-values.\n", "\n", "    Sub-question (a): Consider modifying the architecture for better performance.\n", "    \"\"\"\n", "\n", "    def __init__(self, input_shape, num_actions):\n", "        super(Q<PERSON>work, self).__init__()\n", "        # Convolutional layers\n", "        self.conv1 = nn.Conv2d(input_shape[0], 32, kernel_size=8, stride=4)\n", "        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2)\n", "        self.conv3 = nn.Conv2d(64, 64, kernel_size=3, stride=1)\n", "\n", "        # Fully connected layers\n", "        self.fc1 = nn.Linear(self._get_conv_out(input_shape), 512)\n", "        self.fc2 = nn.Linear(512, num_actions)\n", "\n", "    def _get_conv_out(self, shape):\n", "        o = self.conv1(torch.zeros(1, *shape))\n", "        o = self.conv2(o)\n", "        o = self.conv3(o)\n", "        return int(np.prod(o.size()))\n", "\n", "    def forward(self, x):\n", "        x = x / 255.0  # Normalize\n", "        x = <PERSON>.relu(self.conv1(x))\n", "        x = <PERSON>.relu(self.conv2(x))\n", "        x = <PERSON>.relu(self.conv3(x))\n", "        x = x.view(x.size(0), -1)\n", "        x = F.relu(self.fc1(x))\n", "        return self.fc2(x)\n", "\n", "\n", "class DQNAgent:\n", "    \"\"\"DQN Agent that learns to play <PERSON><PERSON>.\n", "\n", "    Sub-question (a): Key methods to modify for algorithm improvements:\n", "    - learn(): Implement Double DQN, different loss functions, etc.\n", "    - select_action(): Implement better exploration strategies\n", "    \"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        state_dim,\n", "        action_dim,\n", "        capacity=REPLAY_BUFFER_CAPACITY,\n", "        batch_size=BATCH_SIZE,\n", "        gamma=GAMMA,\n", "        learning_rate=LEARNING_RATE,\n", "    ):\n", "        self.state_dim = state_dim\n", "        self.action_dim = action_dim\n", "        self.batch_size = batch_size\n", "        self.gamma = gamma\n", "        self.memory = ReplayBuffer(capacity)\n", "\n", "        # Two networks for stability\n", "        self.policy_net = QNetwork(state_dim, action_dim)\n", "        self.target_net = QNetwork(state_dim, action_dim)\n", "        self.target_net.load_state_dict(self.policy_net.state_dict())\n", "        self.target_net.eval()\n", "\n", "        self.optimizer = optim.Adam(self.policy_net.parameters(), lr=learning_rate)\n", "\n", "        # Sub-question (a): Choose loss function\n", "        # self.loss_fn = nn.MSELoss()\n", "        self.loss_fn = nn.SmoothL1Loss()  # Huber loss\n", "\n", "        self.last_loss = 0.0\n", "\n", "    def select_action(self, state, epsilon):\n", "        \"\"\"Epsilon-greedy action selection.\n", "\n", "        Sub-question (a): Consider implementing better exploration strategies.\n", "        \"\"\"\n", "        if random.random() < epsilon:\n", "            return random.randrange(self.action_dim)\n", "\n", "        with torch.no_grad():\n", "            # <--- 修改：将状态张量移动到正确的设备\n", "            state_tensor = torch.tensor(state, dtype=torch.float32).unsqueeze(0).to(device)\n", "            q_values = self.policy_net(state_tensor)\n", "            return q_values.max(1)[1].item()\n", "\n", "    def learn(self):\n", "        \"\"\"Update the policy network.\n", "        \"\"\"\n", "        if len(self.memory) < self.batch_size:\n", "            return\n", "\n", "        states, actions, rewards, next_states, dones = self.memory.sample(\n", "            self.batch_size\n", "        )\n", "\n", "        # <--- 新增：将从缓冲区采样的数据移动到GPU\n", "        states = states.to(device)\n", "        actions = actions.to(device)\n", "        rewards = rewards.to(device)\n", "        next_states = next_states.to(device)\n", "        dones = dones.to(device)\n", "\n", "\n", "        # Current Q values\n", "        current_q_values = self.policy_net(states).gather(1, actions.unsqueeze(1))\n", "\n", "        # Next Q values from target network\n", "        with torch.no_grad():\n", "            next_q_values = self.target_net(next_states).max(1)[0]\n", "            next_q_values[dones] = 0.0\n", "            target_q_values = rewards + (self.gamma * next_q_values)\n", "\n", "        # Compute loss\n", "        loss = self.loss_fn(current_q_values, target_q_values.unsqueeze(1))\n", "        self.last_loss = loss.item()\n", "\n", "        # Optimize\n", "        self.optimizer.zero_grad()\n", "        loss.backward()\n", "        self.optimizer.step()\n", "\n", "    def update_target_network(self):\n", "        \"\"\"Copy weights from policy network to target network.\"\"\"\n", "        self.target_net.load_state_dict(self.policy_net.state_dict())"]}, {"cell_type": "markdown", "metadata": {"id": "iNHDQTJpuBSQ"}, "source": ["### Cell 4: Training Loop\n", "\n", "**For sub-question (a)**: MODIFY THIS CELL for **training strategies**.\n", "\n", "Possible improvements:\n", "- Implement different frame stacking strategies\n", "- Add warm-up phase before learning starts\n", "- Modify learning frequency\n", "- Implement reward shaping\n", "- Add training tricks like gradient clipping"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "7KKz0bhJuBSR", "outputId": "d5ab8d6e-ce6c-479c-8d7f-3b1b5911c0e3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: pygame in /usr/local/lib/python3.11/dist-packages (2.6.1)\n", "--- Training on device: cuda ---\n", "--- Training Started ---\n", "\n", "Episode: 1/50, Score: -81.30, Epsilon: 1.00, Steps: 4\n", "Episode: 2/50, Score: -73.80, Epsilon: 1.00, Steps: 13\n", "Episode: 3/50, Score: -91.20, Epsilon: 1.00, Steps: 16\n", "Episode: 4/50, Score: -45.60, Epsilon: 1.00, Steps: 33\n", "Episode: 5/50, Score: -80.20, Epsilon: 1.00, Steps: 36\n", "--- Step 50: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_50.pth\n", "Episode: 6/50, Score: -67.80, Epsilon: 0.99, Steps: 55\n", "Episode: 7/50, Score: -20.40, Epsilon: 0.99, Steps: 80\n", "Episode: 8/50, Score: -91.10, Epsilon: 0.99, Steps: 82\n", "Episode: 9/50, Score: -63.00, Epsilon: 0.99, Steps: 93\n", "--- Step 100: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_100.pth\n", "Episode: 10/50, Score: 389.90, Epsilon: 0.99, Steps: 115\n", "Episode: 11/50, Score: -100.00, Epsilon: 0.99, Steps: 116\n", "Episode: 12/50, Score: -39.80, Epsilon: 0.99, Steps: 135\n", "--- Step 150: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_150.pth\n", "Episode: 13/50, Score: -56.60, Epsilon: 0.99, Steps: 152\n", "Episode: 14/50, Score: 581.80, Epsilon: 0.99, Steps: 195\n", "Episode: 15/50, Score: -91.10, Epsilon: 0.99, Steps: 197\n", "--- Step 200: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_200.pth\n", "Episode: 16/50, Score: -91.20, Epsilon: 0.98, Steps: 200\n", "Episode: 17/50, Score: -88.10, Epsilon: 0.98, Steps: 212\n", "--- Step 250: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_250.pth\n", "Episode: 18/50, Score: 86.70, Epsilon: 0.98, Steps: 266\n", "Episode: 19/50, Score: -81.30, Epsilon: 0.98, Steps: 270\n", "--- Step 300: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_300.pth\n", "Episode: 20/50, Score: 45.90, Epsilon: 0.98, Steps: 302\n", "Episode: 21/50, Score: -49.80, Epsilon: 0.98, Steps: 321\n", "Episode: 22/50, Score: -82.60, Epsilon: 0.98, Steps: 328\n", "Episode: 23/50, Score: -37.50, Epsilon: 0.98, Steps: 344\n", "--- Step 350: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_350.pth\n", "Episode: 24/50, Score: -64.00, Epsilon: 0.98, Steps: 355\n", "Episode: 25/50, Score: -66.40, Epsilon: 0.98, Steps: 370\n", "--- Step 400: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_400.pth\n", "Episode: 26/50, Score: -25.90, Epsilon: 0.97, Steps: 400\n", "Episode: 27/50, Score: -100.00, Epsilon: 0.97, Steps: 401\n", "Episode: 28/50, Score: -90.10, Epsilon: 0.97, Steps: 403\n", "Episode: 29/50, Score: -71.50, Epsilon: 0.97, Steps: 409\n", "Episode: 30/50, Score: -63.20, Epsilon: 0.97, Steps: 422\n", "Episode: 31/50, Score: -100.00, Epsilon: 0.97, Steps: 423\n", "Episode: 32/50, Score: -70.60, Epsilon: 0.97, Steps: 430\n", "Episode: 33/50, Score: -80.20, Epsilon: 0.97, Steps: 433\n", "--- Step 450: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_450.pth\n", "Episode: 34/50, Score: -37.20, Epsilon: 0.97, Steps: 456\n", "Episode: 35/50, Score: -73.60, Epsilon: 0.97, Steps: 463\n", "Episode: 36/50, Score: -67.10, Epsilon: 0.96, Steps: 475\n", "Episode: 37/50, Score: -91.10, Epsilon: 0.96, Steps: 477\n", "Episode: 38/50, Score: -81.30, Epsilon: 0.96, Steps: 481\n", "Episode: 39/50, Score: -81.30, Epsilon: 0.96, Steps: 485\n", "--- Step 500: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_500.pth\n", "Episode: 40/50, Score: -72.50, Epsilon: 0.96, Steps: 521\n", "Episode: 41/50, Score: -64.90, Epsilon: 0.96, Steps: 531\n", "Episode: 42/50, Score: -22.10, Epsilon: 0.96, Steps: 543\n", "Episode: 43/50, Score: -91.20, Epsilon: 0.96, Steps: 546\n", "Episode: 44/50, Score: -100.00, Epsilon: 0.96, Steps: 547\n", "--- Step 550: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_550.pth\n", "Episode: 45/50, Score: -91.20, Epsilon: 0.96, Steps: 550\n", "Episode: 46/50, Score: -100.00, Epsilon: 0.96, Steps: 551\n", "Episode: 47/50, Score: -34.30, Epsilon: 0.95, Steps: 595\n", "--- Step 600: Target Network Updated! ---\n", "Model saved to pacman_dqn_model_600.pth\n", "Episode: 48/50, Score: -71.40, Epsilon: 0.95, Steps: 600\n", "Episode: 49/50, Score: -90.10, Epsilon: 0.95, Steps: 602\n", "Episode: 50/50, Score: -91.10, Epsilon: 0.95, Steps: 604\n", "\n", "--- Training Complete ---\n", "Total time: 0.59 minutes\n", "Model saved to pacman_dqn_model.pth\n"]}], "source": ["import time\n", "\n", "# <--- 新增：在 Colab 中安装 pygame\n", "!pip install pygame\n", "\n", "# --- Initialize Environment and Agent ---\n", "env = PacmanGame()\n", "\n", "# Sub-question (a): Modify frame stacking strategy\n", "# Currently stacking 2 frames - consider different numbers or methods\n", "frame_stack_size = FRAME_STACK_SIZE\n", "\n", "# Get state and action dimensions\n", "state_dim = (frame_stack_size, SCREEN_HEIGHT, SCREEN_WIDTH)\n", "action_dim = env.action_space\n", "\n", "# Create the DQN agent\n", "agent = DQNAgent(\n", "    state_dim=state_dim,\n", "    action_dim=action_dim,\n", ")\n", "\n", "# <--- 新增：设置设备并移动模型到GPU\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "agent.policy_net.to(device)\n", "agent.target_net.to(device)\n", "# <--- 新增：将我们定义的全局device变量（在Cell 3中）也更新\n", "globals()['device'] = device\n", "print(f\"--- Training on device: {device} ---\")\n", "\n", "\n", "# Initialize training variables\n", "epsilon = EPSILON_START\n", "total_steps = 0\n", "training_info = {}\n", "\n", "# Sub-question (a): Consider implementing a warm-up phase\n", "# For example: min_buffer_size = 1000\n", "# Don't start learning until buffer has enough samples\n", "\n", "\n", "\n", "# --- Start Training Loop ---\n", "print(\"--- Training Started ---\\n\")\n", "start_time = time.time()\n", "\n", "for episode in range(NUM_EPISODES):\n", "    # Reset environment and initialize frame stack\n", "    frame = env.reset()\n", "    frame_stack = deque([frame] * frame_stack_size, maxlen=frame_stack_size)\n", "    state = np.concatenate(frame_stack, axis=0)\n", "    episode_reward = 0\n", "\n", "    while True:\n", "        total_steps += 1\n", "\n", "        # Select and execute action\n", "        action = agent.select_action(state, epsilon)\n", "        next_frame, reward, done = env.step(action)\n", "\n", "        # Sub-question (a): Add reward shaping here if desired\n", "        # For example: shaped_reward = reward + custom_bonus\n", "\n", "        # Update frame stack\n", "        frame_stack.append(next_frame)\n", "        next_state = np.concatenate(frame_stack, axis=0)\n", "\n", "        # Store experience\n", "        agent.memory.push(state, action, reward, next_state, done)\n", "\n", "        # Update state\n", "        state = next_state\n", "        episode_reward += reward\n", "\n", "        # Sub-question (a): Modify learning frequency\n", "        # Currently learning every step - consider different strategies\n", "        if total_steps % LEARN_EVERY_N_STEPS == 0:\n", "            # <--- 修改：确保在learn之前，agent的长度至少大于batch_size\n", "            if len(agent.memory) > BATCH_SIZE:\n", "                 agent.learn()\n", "\n", "        # <--- 修改：注释掉渲染，因为它在Colab中无法工作\n", "        # env.render(training_info)\n", "\n", "        # Update target network\n", "        if total_steps % TARGET_UPDATE_STEPS == 0:\n", "            agent.update_target_network()\n", "            print(f\"--- Step {total_steps}: Target Network Updated! ---\")\n", "            MODEL_PATH = f\"pacman_dqn_model_{total_steps}.pth\"\n", "            torch.save(agent.policy_net.state_dict(), MODEL_PATH)\n", "            print(f\"Model saved to {MODEL_PATH}\")\n", "\n", "        if done:\n", "            break\n", "\n", "    # Decay epsilon\n", "    epsilon = max(EPSILON_END, epsilon * EPSILON_DECAY)\n", "\n", "    print(\n", "        f\"Episode: {episode + 1}/{NUM_EPISODES}, Score: {episode_reward:.2f}, Epsilon: {epsilon:.2f}, Steps: {total_steps}\"\n", "    )\n", "\n", "# --- Training Complete ---\n", "end_time = time.time()\n", "print(\"\\n--- Training Complete ---\")\n", "print(f\"Total time: {(end_time - start_time) / 60:.2f} minutes\")\n", "\n", "# Save final model\n", "MODEL_PATH = \"pacman_dqn_model.pth\"\n", "torch.save(agent.policy_net.state_dict(), MODEL_PATH)\n", "print(f\"Model saved to {MODEL_PATH}\")\n", "\n", "# <--- 修改：注释掉pygame.quit()\n", "# pygame.quit()"]}, {"cell_type": "markdown", "metadata": {"id": "zwAQVl79uBSR"}, "source": ["### Cell 5: Testing & Evaluation\n", "\n", "**For sub-question (c)**: Use this cell to evaluate your agent's performance.\n", "Run this after training to collect metrics for your report."]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "lHu9BipwuBSR", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "bd3d72c7-c81d-41ea-ba02-fc36bde3bbf6"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Model loaded successfully from pacman_dqn_model.pth to device cuda!\n", "\n", "--- Testing Started ---\n", "\n", "Test Episode: 1/10, Score: -90.10\n", "Test Episode: 2/10, Score: -41.70\n", "Test Episode: 3/10, Score: -90.10\n", "Test Episode: 4/10, Score: -34.30\n", "Test Episode: 5/10, Score: -60.40\n", "Test Episode: 6/10, Score: -90.10\n", "Test Episode: 7/10, Score: -60.40\n", "Test Episode: 8/10, Score: -61.50\n", "Test Episode: 9/10, Score: -22.70\n", "Test Episode: 10/10, Score: -30.70\n", "\n", "--- Testing Complete ---\n", "Average Score: -58.20\n", "Best Score: -22.70\n", "Lowest Score: -90.10\n", "Standard Deviation: 24.36\n", "\n", "Testing complete.\n"]}], "source": ["from collections import deque\n", "\n", "import numpy as np\n", "import torch\n", "\n", "# --- Initialize Test Environment and Agent ---\n", "test_env = PacmanGame()\n", "state_dim = (FRAME_STACK_SIZE, SCREEN_HEIGHT, SCREEN_WIDTH)\n", "action_dim = test_env.action_space\n", "test_agent = DQNAgent(state_dim=state_dim, action_dim=action_dim)\n", "\n", "# --- Load Trained Model ---\n", "MODEL_PATH = \"pacman_dqn_model.pth\"\n", "try:\n", "    # <--- 修改：这里的 device 逻辑本身就是正确的\n", "    device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "    test_agent.policy_net.load_state_dict(\n", "        torch.load(MODEL_PATH, map_location=device)\n", "    )\n", "    test_agent.policy_net.to(device)\n", "    test_agent.policy_net.eval()\n", "    print(f\"\\nModel loaded successfully from {MODEL_PATH} to device {device}!\")\n", "except FileNotFoundError:\n", "    print(f\"Error: Model file {MODEL_PATH} not found. Please run training first.\")\n", "    # pygame.quit()\n", "    exit()\n", "\n", "# --- Run Test Episodes ---\n", "num_test_episodes = 10\n", "test_scores = []\n", "\n", "print(\"\\n--- Testing Started ---\\n\")\n", "running_test = True\n", "\n", "for episode in range(num_test_episodes):\n", "    if not running_test:\n", "        break\n", "\n", "    frame = test_env.reset()\n", "    frame_stack = deque([frame] * FRAME_STACK_SIZE, maxlen=FRAME_STACK_SIZE)\n", "    # <--- 修改：测试时也需要将 state 移动到 GPU\n", "    state = np.concatenate(frame_stack, axis=0)\n", "    episode_reward = 0\n", "    done = False\n", "\n", "    while not done:\n", "        # <--- 注释掉事件检查，因为没有窗口\n", "        # for event in pygame.event.get():\n", "        #     if event.type == pygame.QUIT:\n", "        #         done = True\n", "        #         running_test = False\n", "\n", "        if not running_test:\n", "            break\n", "\n", "        # Greedy action selection (no exploration)\n", "        # <--- 修改： select_action 已经内置了 .to(device)\n", "        action = test_agent.select_action(state, epsilon=0.0)\n", "        next_frame, reward, game_is_done = test_env.step(action)\n", "\n", "        frame_stack.append(next_frame)\n", "        next_state = np.concatenate(frame_stack, axis=0)\n", "        state = next_state\n", "        episode_reward += reward\n", "\n", "        # <--- 修改：注释掉渲染\n", "        # test_env.render(test_info)\n", "\n", "        if game_is_done:\n", "            done = True\n", "\n", "    if running_test:\n", "        test_scores.append(episode_reward)\n", "        print(\n", "            f\"Test Episode: {episode + 1}/{num_test_episodes}, Score: {episode_reward:.2f}\"\n", "        )\n", "\n", "# --- Print Results ---\n", "if test_scores:\n", "    print(\"\\n--- Testing Complete ---\")\n", "    print(f\"Average Score: {np.mean(test_scores):.2f}\")\n", "    print(f\"Best Score: {np.max(test_scores):.2f}\")\n", "    print(f\"Lowest Score: {np.min(test_scores):.2f}\")\n", "    print(f\"Standard Deviation: {np.std(test_scores):.2f}\")\n", "\n", "print(\"\\nTesting complete.\")\n", "# <--- 修改：注释掉 pygame.quit()\n", "# pygame.quit()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "bYa_inNKuBSS"}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}, "colab": {"provenance": [], "gpuType": "L4"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}